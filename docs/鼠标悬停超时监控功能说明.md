# 鼠标悬停超时监控功能说明

## 功能概述

在 `scripts/start_auto_recording_with_lifecycle.py` 中新增了鼠标悬停超时监控功能，用于检测控件识别性能问题并自动重启 `auto_recording_manager.py` 进程。

## 工作原理

### 监控流程

1. **鼠标移动检测**: 监听全局鼠标移动事件
2. **悬停检测**: 当鼠标停止移动超过指定阈值（默认0.5秒）时，触发悬停检测
3. **超时监控**: 悬停检测后，启动超时计时器（默认2秒）
4. **重启触发**: 如果超时时间内没有新的鼠标移动，认为控件识别可能卡住，触发管理器重启

### 技术实现

- **HoverTimeoutMonitor类**: 独立的悬停超时监控器
- **pynput库**: 用于全局鼠标事件监听
- **线程安全**: 使用锁机制确保多线程安全
- **定时器机制**: 使用 `threading.Timer` 实现精确的时间控制

## 配置参数

### 命令行参数

```bash
--hover-threshold HOVER_THRESHOLD
    鼠标悬停检测阈值（秒），默认0.5
    
--hover-timeout HOVER_TIMEOUT  
    悬停后控件识别超时阈值（秒），默认2.0
    
--disable-hover-monitor
    禁用鼠标悬停监控
```

### 参数说明

- **hover-threshold**: 鼠标停止移动多长时间后开始悬停检测
- **hover-timeout**: 悬停检测后多长时间内没有鼠标移动就认为超时
- **disable-hover-monitor**: 完全禁用悬停监控功能

## 使用示例

### 基本使用

```bash
# 使用默认参数启动（悬停0.5秒，超时2秒）
DISPLAY=:0 python3 scripts/start_auto_recording_with_lifecycle.py

# 自定义悬停参数
DISPLAY=:0 python3 scripts/start_auto_recording_with_lifecycle.py \
    --hover-threshold 1.0 \
    --hover-timeout 3.0

# 禁用悬停监控
DISPLAY=:0 python3 scripts/start_auto_recording_with_lifecycle.py \
    --disable-hover-monitor
```

### 调试模式

```bash
# 启用调试模式查看详细日志
DISPLAY=:0 python3 scripts/start_auto_recording_with_lifecycle.py \
    --debug \
    --hover-threshold 0.5 \
    --hover-timeout 2.0
```

## 监控统计

### 状态报告

程序运行时会定期输出状态报告，包含悬停监控统计：

```
[MAIN] 📈 状态报告:
   运行状态: 正常
   管理器活跃: 是
   重启次数: 0/5
   总识别次数: 15
   慢识别次数: 0
   当前连续慢识别: 0/1
   悬停监控状态: 运行中
   悬停检测次数: 3
   悬停超时次数: 1
   悬停触发重启次数: 1
```

### 统计指标说明

- **悬停检测次数**: 检测到鼠标悬停的总次数
- **悬停超时次数**: 悬停后超时的次数
- **悬停触发重启次数**: 由悬停超时触发的重启次数

## 日志输出

### 正常运行日志

```
[HOVER] 🎯 鼠标悬停监控已启动
[HOVER] 📊 悬停阈值: 0.5秒, 超时阈值: 2.0秒
```

### 悬停检测日志

```
[DEBUG] 🎯 检测到悬停: (680, 824)
```

### 超时重启日志

```
[HOVER] 🚨 悬停超时检测: 位置=(680, 824), 耗时=2.5秒
[HOVER] 🔄 触发管理器重启 (第1次)
```

## 依赖要求

### 必需依赖

- **pynput**: 用于全局鼠标事件监听
- **X11环境**: 需要图形界面环境
- **DISPLAY环境变量**: 必须正确设置

### 安装依赖

```bash
pip3 install pynput
```

## 故障排除

### 常见问题

1. **pynput不可用**
   ```
   [WARNING] pynput不可用，鼠标悬停监控将被禁用
   ```
   解决方案: 安装pynput库 `pip3 install pynput`

2. **X11连接失败**
   ```
   Unable to init server: Could not connect: Connection refused
   ```
   解决方案: 确保DISPLAY环境变量正确设置 `export DISPLAY=:0`

3. **权限问题**
   ```
   [ERROR] 启动鼠标悬停监控失败: Permission denied
   ```
   解决方案: 确保有访问X11服务器的权限

### 调试建议

1. 使用 `--debug` 参数查看详细日志
2. 使用 `scripts/test_hover_monitor.py` 测试悬停监控功能
3. 检查pynput库是否正确安装和工作

## 性能影响

### 资源消耗

- **CPU使用**: 极低，仅在鼠标移动时处理事件
- **内存使用**: 约1-2MB额外内存
- **网络影响**: 无

### 优化特性

- **智能过滤**: 只有移动距离超过5像素才触发重新计时
- **线程安全**: 使用锁机制避免竞态条件
- **资源清理**: 程序退出时自动清理所有资源

## 与现有功能的集成

### 双重保护机制

1. **性能监控**: 基于stderr日志分析的慢识别检测
2. **悬停监控**: 基于鼠标行为的超时检测

### 重启策略

- 两种监控机制共享同一个重启计数器
- 重启冷却时间对两种触发方式都有效
- 最大重启次数限制适用于所有重启触发

## 总结

鼠标悬停超时监控功能为GAT全流程录制系统提供了额外的可靠性保障，能够及时发现和解决控件识别卡住的问题，确保录制过程的稳定性和用户体验。
